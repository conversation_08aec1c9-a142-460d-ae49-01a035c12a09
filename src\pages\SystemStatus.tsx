import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/lib/supabase';
import { testUsers, checkSupabaseConnection, checkAllUsersExist, testAllUsers } from '@/utils/testAuth';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  Users, 
  Shield,
  Activity,
  RefreshCw
} from 'lucide-react';

interface SystemCheck {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
  details?: string;
}

const SystemStatus = () => {
  const { user, profile, loading: authLoading } = useAuth();
  const [checks, setChecks] = useState<SystemCheck[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const initialChecks: SystemCheck[] = [
    { name: 'Supabase Connection', status: 'loading', message: 'Checking...' },
    { name: 'Authentication System', status: 'loading', message: 'Checking...' },
    { name: 'User Database', status: 'loading', message: 'Checking...' },
    { name: 'Test Users', status: 'loading', message: 'Checking...' },
    { name: 'Role-based Access', status: 'loading', message: 'Checking...' }
  ];

  useEffect(() => {
    runSystemChecks();
  }, []);

  const updateCheck = (name: string, updates: Partial<SystemCheck>) => {
    setChecks(prev => prev.map(check => 
      check.name === name ? { ...check, ...updates } : check
    ));
  };

  const runSystemChecks = async () => {
    setIsRunning(true);
    setChecks(initialChecks);

    try {
      // Check 1: Supabase Connection
      const supabaseConnected = await checkSupabaseConnection();
      updateCheck('Supabase Connection', {
        status: supabaseConnected ? 'success' : 'error',
        message: supabaseConnected ? 'Connected' : 'Connection failed',
        details: supabaseConnected ? 'Supabase client initialized and responsive' : 'Check environment variables'
      });

      // Check 2: Authentication System
      if (supabaseConnected) {
        try {
          const { data } = await supabase!.auth.getSession();
          updateCheck('Authentication System', {
            status: 'success',
            message: 'Operational',
            details: `Current session: ${data.session ? 'Active' : 'None'}`
          });
        } catch (error) {
          updateCheck('Authentication System', {
            status: 'error',
            message: 'Auth system error',
            details: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        updateCheck('Authentication System', {
          status: 'error',
          message: 'Cannot test - Supabase not connected'
        });
      }

      // Check 3: User Database
      if (supabaseConnected) {
        try {
          const { data, error } = await supabase!.from('profiles').select('count').limit(1);
          updateCheck('User Database', {
            status: error ? 'error' : 'success',
            message: error ? 'Database error' : 'Accessible',
            details: error ? error.message : 'Profiles table accessible'
          });
        } catch (error) {
          updateCheck('User Database', {
            status: 'error',
            message: 'Database connection failed',
            details: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        updateCheck('User Database', {
          status: 'error',
          message: 'Cannot test - Supabase not connected'
        });
      }

      // Check 4: Test Users
      if (supabaseConnected) {
        try {
          let existingUsers = 0;
          for (const testUser of testUsers) {
            const { data } = await supabase!
              .from('profiles')
              .select('email')
              .eq('email', testUser.email)
              .single();
            if (data) existingUsers++;
          }

          updateCheck('Test Users', {
            status: existingUsers === testUsers.length ? 'success' : 'warning',
            message: `${existingUsers}/${testUsers.length} users found`,
            details: existingUsers === testUsers.length ? 'All test users exist' : 'Some test users missing'
          });
        } catch (error) {
          updateCheck('Test Users', {
            status: 'error',
            message: 'User check failed',
            details: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        updateCheck('Test Users', {
          status: 'error',
          message: 'Cannot test - Supabase not connected'
        });
      }

      // Check 5: Role-based Access
      updateCheck('Role-based Access', {
        status: user && profile ? 'success' : 'warning',
        message: user && profile ? 'Working' : 'Not authenticated',
        details: user && profile ? `Current user: ${profile.role}` : 'Login to test role-based access'
      });

    } catch (error) {
      console.error('System check error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: SystemCheck['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error': return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'loading': return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
    }
  };

  const getStatusColor = (status: SystemCheck['status']) => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'loading': return 'bg-blue-50 border-blue-200';
    }
  };

  const runAuthTests = async () => {
    setIsRunning(true);
    try {
      await testAllUsers();
    } catch (error) {
      console.error('Auth test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">System Status</h1>
          <p className="text-gray-600 mt-2">Comprehensive system health check</p>
        </div>

        {/* Current User Info */}
        {user && (
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Logged in as: <strong>{profile?.full_name || user.email}</strong> 
              {profile?.role && <Badge className="ml-2">{profile.role}</Badge>}
            </AlertDescription>
          </Alert>
        )}

        {/* System Checks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              System Health Checks
              <Button 
                onClick={runSystemChecks} 
                disabled={isRunning}
                variant="outline"
                size="sm"
              >
                {isRunning ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Activity className="w-4 h-4 mr-2" />
                )}
                {isRunning ? 'Running...' : 'Refresh'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {checks.map((check, index) => (
                <div 
                  key={index} 
                  className={`p-4 rounded-lg border ${getStatusColor(check.status)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(check.status)}
                      <div>
                        <h3 className="font-medium">{check.name}</h3>
                        <p className="text-sm text-gray-600">{check.message}</p>
                        {check.details && (
                          <p className="text-xs text-gray-500 mt-1">{check.details}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Test Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Button 
                onClick={runAuthTests}
                disabled={isRunning}
                variant="outline"
                className="w-full"
              >
                <Users className="w-4 h-4 mr-2" />
                Test All User Logins
              </Button>
              
              <Button 
                onClick={() => window.open('/user-management', '_blank')}
                variant="outline"
                className="w-full"
              >
                <Database className="w-4 h-4 mr-2" />
                User Management
              </Button>
            </div>

            <div className="text-sm text-gray-600">
              <p><strong>Test Credentials:</strong></p>
              <ul className="mt-2 space-y-1">
                {testUsers.map((user, index) => (
                  <li key={index} className="font-mono text-xs">
                    {user.email} / {user.password} ({user.role})
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SystemStatus;
