import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface KPIItem {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'default' | 'green' | 'red' | 'yellow' | 'blue';
}

interface KPIGridProps {
  items: KPIItem[];
  columns?: 2 | 3 | 4;
  loading?: boolean;
}

const KPIGrid: React.FC<KPIGridProps> = ({ 
  items, 
  columns = 4,
  loading = false 
}) => {
  const getValueColor = (color?: string) => {
    switch (color) {
      case 'green': return 'text-green-600';
      case 'red': return 'text-red-600';
      case 'yellow': return 'text-yellow-600';
      case 'blue': return 'text-blue-600';
      default: return 'text-gray-900';
    }
  };

  const formatValue = (value: string | number) => {
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    return value;
  };

  if (loading) {
    return (
      <div className={`grid md:grid-cols-${columns} gap-6 mb-8`}>
        {Array.from({ length: columns }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid md:grid-cols-${columns} gap-6 mb-8`}>
      {items.map((item, index) => {
        const Icon = item.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getValueColor(item.color)}`}>
                {formatValue(item.value)}
              </div>
              {item.subtitle && (
                <p className="text-xs text-muted-foreground">{item.subtitle}</p>
              )}
              {item.trend && (
                <div className={`flex items-center text-xs mt-1 ${
                  item.trend.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  <span className="mr-1">
                    {item.trend.isPositive ? '↗' : '↘'}
                  </span>
                  {Math.abs(item.trend.value)}%
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default KPIGrid;
