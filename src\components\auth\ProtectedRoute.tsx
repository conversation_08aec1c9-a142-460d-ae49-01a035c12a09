import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthProvider';
import { PageLoading } from '@/components/ui/loading';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'franchisor' | 'franchisee' | 'admin';
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  redirectTo = '/login'
}) => {
  const { user, profile, loading, devMode } = useAuth();
  const location = useLocation();

  // Show loading while checking authentication
  if (loading) {
    return <PageLoading />;
  }

  // In dev mode, allow access to all routes
  if (devMode) {
    return <>{children}</>;
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRole && profile?.role !== requiredRole) {
    // Redirect based on user's actual role
    const roleRedirects = {
      franchisor: '/franchisor-dashboard',
      franchisee: '/franchisee-dashboard',
      admin: '/admin-dashboard'
    };

    const userRedirect = roleRedirects[profile?.role as keyof typeof roleRedirects] || '/';
    return <Navigate to={userRedirect} replace />;
  }

  return <>{children}</>;
};

// Convenience components for specific roles
export const FranchisorRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRole="franchisor">{children}</ProtectedRoute>
);

export const FranchiseeRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRole="franchisee">{children}</ProtectedRoute>
);

export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRole="admin">{children}</ProtectedRoute>
);

export default ProtectedRoute;
