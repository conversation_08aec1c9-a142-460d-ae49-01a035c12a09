// <PERSON><PERSON>t to create test users for development
// Run this in the browser console on the user management page

import { createDefaultUsers } from '@/utils/createUsers';

// Function to create test users
export const runUserCreation = async () => {
  console.log('🚀 Starting user creation process...');
  
  try {
    const results = await createDefaultUsers();
    
    console.log('📊 User Creation Results:');
    results.forEach((result, index) => {
      const userEmails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>'
      ];
      
      if (result.success) {
        console.log(`✅ ${userEmails[index]}: Created successfully`);
      } else {
        console.log(`❌ ${userEmails[index]}: ${result.error}`);
      }
    });
    
    console.log('🎉 User creation process completed!');
    return results;
    
  } catch (error) {
    console.error('💥 Error during user creation:', error);
    throw error;
  }
};

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  console.log('🔧 Test user creation script loaded. Run runUserCreation() to create users.');
}

// Test credentials for reference
export const testCredentials = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123',
    role: 'admin'
  },
  owner: {
    email: '<EMAIL>',
    password: 'Owner123',
    role: 'franchisor'
  },
  franchisee1: {
    email: '<EMAIL>',
    password: 'Franchisee123',
    role: 'franchisee'
  }
};

console.log('📋 Test Credentials:', testCredentials);
