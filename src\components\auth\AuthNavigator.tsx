import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthProvider';

/**
 * Smart navigation component that handles role-based redirects after authentication
 */
export const AuthNavigator = () => {
  const { user, profile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Don't navigate if still loading
    if (loading) return;

    // Don't navigate if no user
    if (!user) return;

    // Don't navigate if already on a dashboard
    if (location.pathname.includes('dashboard')) return;

    // Get the intended destination from location state
    const from = location.state?.from?.pathname;

    // Determine the correct dashboard based on user role
    const getRoleDashboard = (role: string) => {
      switch (role) {
        case 'admin':
          return '/admin-dashboard';
        case 'franchisor':
          return '/franchisor-dashboard';
        case 'franchisee':
          return '/franchisee-dashboard';
        default:
          return '/';
      }
    };

    // Navigate to appropriate dashboard
    if (profile?.role) {
      const dashboard = getRoleDashboard(profile.role);
      console.log(`Navigating ${profile.role} to ${dashboard}`);
      navigate(dashboard, { replace: true });
    } else if (from) {
      // Navigate to intended destination if no role determined yet
      navigate(from, { replace: true });
    } else {
      // Default to home
      navigate('/', { replace: true });
    }
  }, [user, profile, loading, navigate, location]);

  return null; // This component doesn't render anything
};

/**
 * Hook for getting role-based navigation paths
 */
export const useRoleNavigation = () => {
  const { profile } = useAuth();

  const getDashboardPath = () => {
    switch (profile?.role) {
      case 'admin':
        return '/admin-dashboard';
      case 'franchisor':
        return '/franchisor-dashboard';
      case 'franchisee':
        return '/franchisee-dashboard';
      default:
        return '/';
    }
  };

  const getDefaultRedirect = () => {
    return getDashboardPath();
  };

  return {
    getDashboardPath,
    getDefaultRedirect,
    role: profile?.role
  };
};

export default AuthNavigator;
