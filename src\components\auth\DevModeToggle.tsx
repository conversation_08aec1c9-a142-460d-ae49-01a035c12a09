import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from './AuthProvider';
import { Settings, User, Crown } from 'lucide-react';

const DevModeToggle: React.FC = () => {
  const { devMode, setDevMode, switchRole, profile } = useAuth();

  if (!import.meta.env.DEV && !devMode) return null;

  return (
    <Card className="fixed bottom-4 right-4 z-50 w-80 bg-yellow-50 border-yellow-200 shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-sm">
          <Settings className="w-4 h-4 text-yellow-600" />
          <span className="text-yellow-800">Developer Mode</span>
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
            DEV
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-yellow-700">Dev Mode:</span>
          <Button
            size="sm"
            variant={devMode ? "default" : "outline"}
            onClick={() => {
              setDevMode(!devMode);
              localStorage.setItem('devMode', (!devMode).toString());
            }}
            className={devMode ? "bg-yellow-600 hover:bg-yellow-700" : ""}
          >
            {devMode ? 'ON' : 'OFF'}
          </Button>
        </div>

        {devMode && (
          <>
            <div className="space-y-2">
              <label className="text-sm text-yellow-700">Switch Role:</label>
              <Select
                value={profile?.role || 'franchisor'}
                onValueChange={(role: 'franchisor' | 'franchisee') => switchRole(role)}
              >
                <SelectTrigger className="bg-white border-yellow-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="franchisor">
                    <div className="flex items-center space-x-2">
                      <Crown className="w-4 h-4 text-purple-600" />
                      <span>Franchisor</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="franchisee">
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-blue-600" />
                      <span>Franchisee</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="text-xs text-yellow-600 bg-yellow-100 p-2 rounded">
              <strong>Current:</strong> {profile?.role || 'Not set'} ({profile?.full_name || 'Dev User'})
            </div>

            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="flex-1 text-xs"
                onClick={() => window.location.href = '/franchisor-dashboard'}
              >
                Franchisor
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="flex-1 text-xs"
                onClick={() => window.location.href = '/franchisee-dashboard'}
              >
                Franchisee
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default DevModeToggle;
