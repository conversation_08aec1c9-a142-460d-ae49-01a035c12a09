import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  DollarSign, 
  MapPin, 
  Mail, 
  MessageSquare, 
  BarChart3,
  Settings,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Zap
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'payment' | 'maps' | 'communication' | 'analytics' | 'accounting';
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'error';
  features: string[];
  setupRequired: boolean;
  apiKey?: string;
}

const ThirdPartyIntegrations: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Accept PayPal payments for franchise fees and royalties',
      category: 'payment',
      icon: <CreditCard className="w-6 h-6 text-blue-600" />,
      status: 'disconnected',
      features: ['Online payments', 'Recurring billing', 'International support'],
      setupRequired: true
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Process credit card payments and subscriptions',
      category: 'payment',
      icon: <DollarSign className="w-6 h-6 text-purple-600" />,
      status: 'connected',
      features: ['Credit cards', 'Bank transfers', 'Mobile payments'],
      setupRequired: false,
      apiKey: 'sk_test_***************'
    },
    {
      id: 'google-maps',
      name: 'Google Maps',
      description: 'Enhanced location services and territory mapping',
      category: 'maps',
      icon: <MapPin className="w-6 h-6 text-green-600" />,
      status: 'connected',
      features: ['Location tracking', 'Territory visualization', 'Route optimization'],
      setupRequired: false,
      apiKey: 'AIza***************'
    },
    {
      id: 'sendgrid',
      name: 'SendGrid',
      description: 'Email notifications and marketing campaigns',
      category: 'communication',
      icon: <Mail className="w-6 h-6 text-blue-500" />,
      status: 'connected',
      features: ['Email templates', 'Automated campaigns', 'Analytics'],
      setupRequired: false
    },
    {
      id: 'twilio',
      name: 'Twilio',
      description: 'SMS notifications and two-factor authentication',
      category: 'communication',
      icon: <MessageSquare className="w-6 h-6 text-red-500" />,
      status: 'disconnected',
      features: ['SMS alerts', '2FA', 'Voice calls'],
      setupRequired: true
    },
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      description: 'Sync financial data and automate accounting',
      category: 'accounting',
      icon: <BarChart3 className="w-6 h-6 text-green-500" />,
      status: 'error',
      features: ['Financial sync', 'Tax reporting', 'Expense tracking'],
      setupRequired: true
    }
  ]);

  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);

  const toggleIntegration = (id: string) => {
    setIntegrations(prev => 
      prev.map(integration => 
        integration.id === id 
          ? { 
              ...integration, 
              status: integration.status === 'connected' ? 'disconnected' : 'connected' 
            }
          : integration
      )
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <Settings className="w-4 h-4 text-gray-500" />;
    }
  };

  const categories = [
    { id: 'all', label: 'All Integrations', icon: Zap },
    { id: 'payment', label: 'Payments', icon: CreditCard },
    { id: 'maps', label: 'Maps & Location', icon: MapPin },
    { id: 'communication', label: 'Communication', icon: MessageSquare },
    { id: 'accounting', label: 'Accounting', icon: BarChart3 }
  ];

  const filteredIntegrations = (category: string) => {
    return category === 'all' 
      ? integrations 
      : integrations.filter(integration => integration.category === category);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-blue-600" />
            <span>Third-Party Integrations</span>
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              {integrations.filter(i => i.status === 'connected').length} Connected
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <TabsTrigger key={category.id} value={category.id}>
                <Icon className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">{category.label}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id}>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredIntegrations(category.id).map((integration) => (
                <Card key={integration.id} className="relative">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {integration.icon}
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <div className="flex items-center space-x-2 mt-1">
                            {getStatusIcon(integration.status)}
                            <Badge className={getStatusColor(integration.status)}>
                              {integration.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={integration.status === 'connected'}
                        onCheckedChange={() => toggleIntegration(integration.id)}
                        disabled={integration.status === 'error'}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{integration.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Features</h4>
                        <div className="space-y-1">
                          {integration.features.map((feature, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span className="text-xs text-gray-600">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {integration.apiKey && (
                        <div>
                          <h4 className="text-sm font-medium mb-2">API Key</h4>
                          <Input 
                            value={integration.apiKey} 
                            readOnly 
                            className="text-xs font-mono"
                          />
                        </div>
                      )}

                      <div className="flex space-x-2">
                        {integration.setupRequired && integration.status !== 'connected' && (
                          <Button size="sm" className="flex-1">
                            <Settings className="w-4 h-4 mr-2" />
                            Setup
                          </Button>
                        )}
                        
                        {integration.status === 'connected' && (
                          <Button size="sm" variant="outline" className="flex-1">
                            <Settings className="w-4 h-4 mr-2" />
                            Configure
                          </Button>
                        )}
                        
                        <Button size="sm" variant="outline">
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>

                      {integration.status === 'error' && (
                        <div className="bg-red-50 border border-red-200 rounded p-3">
                          <div className="flex items-center space-x-2">
                            <AlertCircle className="w-4 h-4 text-red-500" />
                            <span className="text-sm font-medium text-red-800">Connection Error</span>
                          </div>
                          <p className="text-xs text-red-600 mt-1">
                            Authentication failed. Please check your credentials.
                          </p>
                          <Button size="sm" className="mt-2 bg-red-600 hover:bg-red-700">
                            Reconnect
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* Integration Marketplace */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Marketplace</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-4 gap-4">
            {[
              { name: 'Salesforce CRM', category: 'CRM', coming: true },
              { name: 'HubSpot', category: 'Marketing', coming: true },
              { name: 'Slack', category: 'Communication', coming: true },
              { name: 'Zapier', category: 'Automation', coming: true }
            ].map((item, index) => (
              <div key={index} className="p-4 border rounded-lg text-center">
                <h4 className="font-medium">{item.name}</h4>
                <p className="text-sm text-gray-600">{item.category}</p>
                <Badge variant="outline" className="mt-2">
                  Coming Soon
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThirdPartyIntegrations;
