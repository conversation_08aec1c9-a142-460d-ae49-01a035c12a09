
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import Logo from '@/components/Logo';
import ChatAssistant from '@/components/ChatAssistant';
import { useDashboard } from '@/hooks/useSupabase';
import { useAuth } from '@/components/auth/AuthProvider';

// Dashboard Components
import KPIGrid from '@/components/dashboard/KPIGrid';
import TabNavigation from '@/components/dashboard/TabNavigation';

// Franchisee Components
import AchievementMilestones from '@/components/franchisee/AchievementMilestones';
import UpgradePackageBanner from '@/components/franchisee/UpgradePackageBanner';
import QuickActions from '@/components/franchisee/QuickActions';
import AnnouncementsNotices from '@/components/franchisee/AnnouncementsNotices';

// Analytics Components
import EnhancedAnalytics from '@/components/analytics/EnhancedAnalytics';

// Tab Content Components
import SalesUploadContent from '@/components/franchisee/tabs/SalesUploadContent';
import InventoryContent from '@/components/franchisee/tabs/InventoryContent';
import MarketingContent from '@/components/franchisee/tabs/MarketingContent';
import ContractContent from '@/components/franchisee/tabs/ContractContent';

import {
  TrendingUp,
  Package,
  DollarSign,
  Calendar,
  Upload,
  BookOpen,
  FileText,
  Image as ImageIcon,
  PlusCircle,
  ArrowUp,
  ArrowLeft,
  Bell,
  BarChart3
} from 'lucide-react';

const FranchiseeDashboard = () => {
  const [showUpgrade, setShowUpgrade] = useState(false);
  const { profile } = useAuth();

  // Supabase hooks
  const { stats, loading: dashboardLoading } = useDashboard();

  // Sidebar navigation items
  const sidebarItems = [
    { label: 'Overview', href: '/franchisee-dashboard', icon: TrendingUp, active: true },
    { label: 'Upload Sales', href: '/franchisee/sales-upload', icon: DollarSign },
    { label: 'Order Inventory', href: '/franchisee/inventory-order', icon: Package },
    { label: 'Marketing Assets', href: '/franchisee/marketing-assets', icon: ImageIcon },
    { label: 'Contract & Package', href: '/franchisee/contract-package', icon: FileText },
    { label: 'Support & Requests', href: '/franchisee/support-requests', icon: Phone },
  ];

  // KPI data
  const kpiData = [
    {
      title: "Today's Sales",
      value: `₱${stats?.salesReports?.[0]?.daily_sales?.toLocaleString() || '0'}`,
      subtitle: "Latest sales report",
      icon: DollarSign,
      color: 'green' as const
    },
    {
      title: "Total Sales",
      value: `₱${stats?.totalSales?.toLocaleString() || '0'}`,
      subtitle: "All time sales",
      icon: Calendar
    },
    {
      title: "Transactions",
      value: stats?.totalTransactions || 0,
      subtitle: "Total transactions",
      icon: TrendingUp
    },
    {
      title: "Support Tickets",
      value: stats?.openTickets || 0,
      subtitle: "Open support requests",
      icon: Package,
      color: 'yellow' as const
    }
  ];

  // Tab configuration
  const tabs = [
    {
      value: 'analytics',
      label: 'Analytics',
      shortLabel: 'Stats',
      icon: BarChart3,
      content: <EnhancedAnalytics userType="franchisee" franchiseeName="Siomai Shop - Makati Branch" />
    },
    {
      value: 'overview',
      label: 'Overview',
      shortLabel: 'Home',
      icon: TrendingUp,
      content: (
        <div className="grid md:grid-cols-2 gap-6">
          <QuickActions />
          <AnnouncementsNotices />
        </div>
      )
    },
    {
      value: 'sales',
      label: 'Upload Sales',
      shortLabel: 'Sales',
      icon: Upload,
      content: <SalesUploadContent />
    },
    {
      value: 'inventory',
      label: 'Inventory',
      shortLabel: 'Stock',
      icon: Package,
      content: <InventoryContent />
    },
    {
      value: 'marketing',
      label: 'Marketing',
      shortLabel: 'Media',
      icon: ImageIcon,
      content: <MarketingContent />
    },
    {
      value: 'contract',
      label: 'Contract',
      shortLabel: 'Docs',
      icon: FileText,
      content: <ContractContent />
    }
  ];

  // Header actions
  const headerActions = (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowUpgrade(!showUpgrade)}
        className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-none hover:from-purple-700 hover:to-pink-700"
      >
        <ArrowUp className="w-4 h-4 mr-2" />
        Upgrade Package
      </Button>
      <Button variant="outline" size="sm" asChild>
        <Link to="/franchisee/support-requests">
          <Bell className="w-4 h-4 mr-2" />
          Support
        </Link>
      </Button>
      <Button size="sm" asChild>
        <Link to="/franchisee/sales-upload">
          <PlusCircle className="w-4 h-4 mr-2" />
          Upload Sales
        </Link>
      </Button>
    </>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-lg h-screen sticky top-0">
          <div className="p-6">
            <div className="flex items-center justify-between mb-8">
              <Logo size="md" />
              <Button variant="ghost" asChild size="sm" className="text-gray-900 hover:text-gray-700 p-1">
                <Link to="/">
                  <ArrowLeft className="w-4 h-4" />
                </Link>
              </Button>
            </div>

            <nav className="space-y-2">
              {sidebarItems.map((item, index) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={index}
                    to={item.href}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                      item.active
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
              <Button asChild className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 mt-4">
                <Link to="/franchisee-training">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Training
                </Link>
              </Button>
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {profile?.full_name || 'Robert'}!
              </h1>
              <p className="text-gray-600">Siomai Shop - Makati Branch (Package B)</p>
            </div>
            <div className="flex items-center space-x-4">
              {headerActions}
            </div>
          </div>

          {/* Upgrade Package Banner */}
          <UpgradePackageBanner
            show={showUpgrade}
            onClose={() => setShowUpgrade(false)}
          />

          {/* KPI Cards */}
          <KPIGrid items={kpiData} loading={dashboardLoading} />

          {/* Achievement Milestones */}
          <AchievementMilestones />

          {/* Main Tabs */}
          <TabNavigation tabs={tabs} defaultValue="analytics" />
        </div>
      </div>

      {/* Chat Assistant */}
      <ChatAssistant />
    </div>
  );
};

export default FranchiseeDashboard;
