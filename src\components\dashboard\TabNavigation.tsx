import React from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { LucideIcon } from 'lucide-react';

interface TabItem {
  value: string;
  label: string;
  shortLabel?: string;
  icon: LucideIcon;
  content: React.ReactNode;
}

interface TabNavigationProps {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  defaultValue,
  className = ""
}) => {
  return (
    <Tabs defaultValue={defaultValue || tabs[0]?.value} className={`space-y-6 ${className}`}>
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-2">
        <TabsList className={`grid w-full grid-cols-${tabs.length} bg-gray-50 rounded-lg p-1 gap-1`}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="flex items-center justify-center px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 data-[state=active]:border data-[state=active]:border-blue-100 hover:bg-white/50"
              >
                <Icon className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">{tab.label}</span>
                {tab.shortLabel && (
                  <span className="sm:hidden">{tab.shortLabel}</span>
                )}
              </TabsTrigger>
            );
          })}
        </TabsList>
      </div>

      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value}>
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default TabNavigation;
