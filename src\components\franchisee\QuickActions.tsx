import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, ShoppingCart, Download, BookOpen } from 'lucide-react';

const QuickActions: React.FC = () => {
  const actions = [
    {
      label: 'Upload Today\'s Sales Report',
      href: '/franchisee/sales-upload',
      icon: Upload
    },
    {
      label: 'Order Inventory Items',
      href: '/franchisee/inventory-order',
      icon: ShoppingCart
    },
    {
      label: 'Download Marketing Materials',
      href: '/franchisee/marketing-assets',
      icon: Download
    },
    {
      label: 'Continue Training Modules',
      href: '/franchisee-training',
      icon: BookOpen
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Button 
              key={index}
              className="w-full justify-start" 
              variant="outline" 
              asChild
            >
              <Link to={action.href}>
                <Icon className="w-4 h-4 mr-2" />
                {action.label}
              </Link>
            </Button>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default QuickActions;
