
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import Logo from '@/components/Logo';
import { useAuth } from '@/components/auth/AuthProvider';
import { Menu, User, LogOut, Settings } from 'lucide-react';

const Navigation = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { user, profile, signOut } = useAuth();

  const handleNavClick = (href: string, isRoute: boolean) => {
    if (!isRoute && location.pathname !== '/') {
      // If we're not on homepage and clicking an anchor link, go to homepage first
      window.location.href = `/${href}`;
    }
  };

  const navigationLinks = [
    { href: "#brands", label: "Brands" },
    { href: "#packages", label: "Packages" },
    { href: "#how-it-works", label: "How It Works" },
    { href: "/blog", label: "Blog", isRoute: true },
    { href: "/contact", label: "Contact", isRoute: true }
  ];

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-40" role="navigation" aria-label="Main navigation">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Logo size="md" />

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationLinks.map((link) => (
              link.isRoute ? (
                <Link
                  key={link.href}
                  to={link.href}
                  className="text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded transition-colors"
                >
                  {link.label}
                </Link>
              ) : (
                <a
                  key={link.href}
                  href={link.href}
                  onClick={() => handleNavClick(link.href, false)}
                  className="text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded transition-colors"
                >
                  {link.label}
                </a>
              )
            ))}





            {/* Authentication Section */}
            {user ? (
              <div className="flex items-center space-x-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-2">
                      <User className="w-4 h-4" />
                      <span className="hidden sm:inline">{profile?.full_name || user.email}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link to={profile?.role === 'franchisor' ? '/franchisor-dashboard' : '/franchisee-dashboard'}>
                        <Settings className="w-4 h-4 mr-2" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => signOut()}>
                      <LogOut className="w-4 h-4 mr-2" />
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button asChild variant="outline">
                  <Link to="/login">Sign In</Link>
                </Button>
                <Button asChild className="bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500">
                  <Link to="/apply">Apply Now</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center space-x-2">
            <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Link to="/apply">Apply</Link>
            </Button>

            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <div className="flex flex-col space-y-4 mt-8">
                  <div className="mb-6">
                    <Logo size="md" clickable={false} />
                  </div>

                  <nav className="flex flex-col space-y-4">
                    {navigationLinks.map((link) => (
                      link.isRoute ? (
                        <Link
                          key={link.href}
                          to={link.href}
                          className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors py-2"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {link.label}
                        </Link>
                      ) : (
                        <a
                          key={link.href}
                          href={link.href}
                          className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors py-2"
                          onClick={() => {
                            handleNavClick(link.href, false);
                            setMobileMenuOpen(false);
                          }}
                        >
                          {link.label}
                        </a>
                      )
                    ))}
                  </nav>


                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
