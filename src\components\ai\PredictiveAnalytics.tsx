import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  AlertTriangle,
  CheckCircle,
  Zap,
  BarChart3
} from 'lucide-react';

interface PredictiveInsight {
  id: string;
  title: string;
  prediction: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  type: 'opportunity' | 'risk' | 'trend';
  timeframe: string;
  actionable: boolean;
  recommendation?: string;
}

interface SalesForecasting {
  nextMonth: {
    predicted: number;
    confidence: number;
    trend: 'up' | 'down' | 'stable';
  };
  nextQuarter: {
    predicted: number;
    confidence: number;
    trend: 'up' | 'down' | 'stable';
  };
  peakDays: string[];
  slowDays: string[];
}

const PredictiveAnalytics: React.FC = () => {
  const [insights, setInsights] = useState<PredictiveInsight[]>([]);
  const [forecasting, setForecasting] = useState<SalesForecasting | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Simulate AI analysis
  useEffect(() => {
    const generateInsights = () => {
      const mockInsights: PredictiveInsight[] = [
        {
          id: '1',
          title: 'Sales Peak Prediction',
          prediction: 'Sales likely to increase by 23% next week',
          confidence: 87,
          impact: 'high',
          type: 'opportunity',
          timeframe: 'Next 7 days',
          actionable: true,
          recommendation: 'Increase inventory for Siomai Mix and Sauce Packets'
        },
        {
          id: '2',
          title: 'Inventory Risk Alert',
          prediction: 'Sauce Packets may run out in 3 days',
          confidence: 94,
          impact: 'high',
          type: 'risk',
          timeframe: '3 days',
          actionable: true,
          recommendation: 'Order 50 boxes of Sauce Packets immediately'
        },
        {
          id: '3',
          title: 'Customer Behavior Trend',
          prediction: 'Lunch hour sales trending 15% higher',
          confidence: 76,
          impact: 'medium',
          type: 'trend',
          timeframe: 'Ongoing',
          actionable: true,
          recommendation: 'Consider extending lunch hour promotions'
        },
        {
          id: '4',
          title: 'Seasonal Opportunity',
          prediction: 'Valentine\'s Day boost expected: +35% sales',
          confidence: 82,
          impact: 'high',
          type: 'opportunity',
          timeframe: 'Feb 10-16',
          actionable: true,
          recommendation: 'Prepare special Valentine\'s packaging and promotions'
        }
      ];

      const mockForecasting: SalesForecasting = {
        nextMonth: {
          predicted: 185000,
          confidence: 89,
          trend: 'up'
        },
        nextQuarter: {
          predicted: 520000,
          confidence: 84,
          trend: 'up'
        },
        peakDays: ['Monday', 'Friday', 'Saturday'],
        slowDays: ['Tuesday', 'Wednesday']
      };

      setInsights(mockInsights);
      setForecasting(mockForecasting);
    };

    generateInsights();
  }, []);

  const runAnalysis = async () => {
    setIsAnalyzing(true);
    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 3000));
    setIsAnalyzing(false);
    
    // Add a new insight
    const newInsight: PredictiveInsight = {
      id: Date.now().toString(),
      title: 'Fresh Analysis Complete',
      prediction: 'New market opportunity identified in nearby area',
      confidence: 91,
      impact: 'medium',
      type: 'opportunity',
      timeframe: 'Next month',
      actionable: true,
      recommendation: 'Consider expansion to adjacent business district'
    };
    
    setInsights(prev => [newInsight, ...prev]);
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'risk': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'trend': return <BarChart3 className="w-4 h-4 text-blue-500" />;
      default: return <Target className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <Target className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <span>AI-Powered Predictive Analytics</span>
              <Badge variant="outline" className="bg-purple-100 text-purple-800">
                BETA
              </Badge>
            </div>
            <Button 
              onClick={runAnalysis} 
              disabled={isAnalyzing}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Run Analysis
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Sales Forecasting */}
      {forecasting && (
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <span>Sales Forecasting</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Next Month</span>
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(forecasting.nextMonth.trend)}
                    <span className="font-bold text-lg">₱{forecasting.nextMonth.predicted.toLocaleString()}</span>
                  </div>
                </div>
                <Progress value={forecasting.nextMonth.confidence} className="h-2" />
                <p className="text-xs text-gray-600">{forecasting.nextMonth.confidence}% confidence</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Next Quarter</span>
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(forecasting.nextQuarter.trend)}
                    <span className="font-bold text-lg">₱{forecasting.nextQuarter.predicted.toLocaleString()}</span>
                  </div>
                </div>
                <Progress value={forecasting.nextQuarter.confidence} className="h-2" />
                <p className="text-xs text-gray-600">{forecasting.nextQuarter.confidence}% confidence</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-orange-600" />
                <span>Peak Performance Days</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-green-800 mb-2">Peak Days</h4>
                  <div className="flex flex-wrap gap-2">
                    {forecasting.peakDays.map((day, index) => (
                      <Badge key={index} className="bg-green-100 text-green-800">
                        {day}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-red-800 mb-2">Slow Days</h4>
                  <div className="flex flex-wrap gap-2">
                    {forecasting.slowDays.map((day, index) => (
                      <Badge key={index} className="bg-red-100 text-red-800">
                        {day}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle>AI-Generated Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {insights.map((insight) => (
              <div key={insight.id} className="p-4 border rounded-lg bg-gradient-to-r from-gray-50 to-white">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(insight.type)}
                    <h4 className="font-medium">{insight.title}</h4>
                    <Badge className={getImpactColor(insight.impact)}>
                      {insight.impact} impact
                    </Badge>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{insight.confidence}%</div>
                    <div className="text-xs text-gray-500">confidence</div>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-2">{insight.prediction}</p>
                
                {insight.recommendation && (
                  <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-3">
                    <div className="flex items-center space-x-2 mb-1">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Recommended Action</span>
                    </div>
                    <p className="text-sm text-blue-700">{insight.recommendation}</p>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Timeframe: {insight.timeframe}</span>
                  {insight.actionable && (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Actionable
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PredictiveAnalytics;
