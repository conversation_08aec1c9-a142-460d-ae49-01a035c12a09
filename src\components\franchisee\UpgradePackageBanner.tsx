import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface UpgradePackageBannerProps {
  show: boolean;
  onClose: () => void;
  currentPackage?: string;
  upgradePackage?: string;
  upgradePrice?: string;
  benefits?: string[];
}

const UpgradePackageBanner: React.FC<UpgradePackageBannerProps> = ({
  show,
  onClose,
  currentPackage = "Package B",
  upgradePackage = "Package C - Advanced",
  upgradePrice = "₱130,000",
  benefits = [
    "Larger territory rights",
    "Advanced POS integration", 
    "Priority customer support"
  ]
}) => {
  if (!show) return null;

  return (
    <Card className="mb-6 border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-purple-900">
              Upgrade to {upgradePackage}
            </h3>
            <p className="text-purple-700">
              Get Food Stall setup + POS System + Uniforms for just {upgradePrice} more!
            </p>
            <ul className="text-sm text-purple-600 mt-2">
              {benefits.map((benefit, index) => (
                <li key={index}>• {benefit}</li>
              ))}
            </ul>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Maybe Later
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700" asChild>
              <Link to="/franchisee/contract-package">
                Upgrade Now
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UpgradePackageBanner;
