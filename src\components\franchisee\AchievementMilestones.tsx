import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Award } from 'lucide-react';

interface Milestone {
  title: string;
  progress: number;
  reward: string;
  status: 'In Progress' | 'Achieved' | 'Locked';
}

interface AchievementMilestonesProps {
  milestones?: Milestone[];
}

const defaultMilestones: Milestone[] = [
  { 
    title: 'Top 10 Sales Performance', 
    progress: 85, 
    reward: 'Certificate + ₱5,000 bonus', 
    status: 'In Progress' 
  },
  { 
    title: 'Perfect Compliance Score', 
    progress: 100, 
    reward: 'Recognition Award', 
    status: 'Achieved' 
  },
  { 
    title: 'Customer Satisfaction Excellence', 
    progress: 67, 
    reward: 'Premium Support Access', 
    status: 'In Progress' 
  }
];

const AchievementMilestones: React.FC<AchievementMilestonesProps> = ({ 
  milestones = defaultMilestones 
}) => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Award className="w-5 h-5 text-yellow-500" />
          <span>Achievement Milestones</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-3 gap-4">
          {milestones.map((milestone, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-sm">{milestone.title}</h4>
                <Badge 
                  className={
                    milestone.status === 'Achieved' 
                      ? 'bg-green-100 text-green-800' 
                      : milestone.status === 'In Progress'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }
                >
                  {milestone.status}
                </Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className={`h-2 rounded-full ${
                    milestone.status === 'Achieved' 
                      ? 'bg-green-600' 
                      : 'bg-blue-600'
                  }`}
                  style={{ width: `${milestone.progress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-600">{milestone.progress}% complete</p>
              <p className="text-xs text-green-600 mt-1">🏆 {milestone.reward}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default AchievementMilestones;
