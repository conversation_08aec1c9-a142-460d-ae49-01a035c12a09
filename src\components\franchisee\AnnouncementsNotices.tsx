import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Notice {
  title: string;
  message: string;
  date: string;
  type: 'info' | 'warning' | 'success' | 'error';
}

interface AnnouncementsNoticesProps {
  notices?: Notice[];
}

const defaultNotices: Notice[] = [
  { 
    title: 'New Product Launch', 
    message: 'Introducing Spicy Siomai variant - now available for order!', 
    date: '2024-01-15', 
    type: 'info' 
  },
  { 
    title: 'Training Reminder', 
    message: 'Monthly compliance training due by January 20th', 
    date: '2024-01-14', 
    type: 'warning' 
  },
  { 
    title: 'Promotion Update', 
    message: 'Valentine\'s Day promo materials now ready for download', 
    date: '2024-01-13', 
    type: 'success' 
  }
];

const AnnouncementsNotices: React.FC<AnnouncementsNoticesProps> = ({ 
  notices = defaultNotices 
}) => {
  const getNoticeType = (type: string) => {
    switch (type) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Announcements & Notices</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notices.map((notice, index) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm">{notice.title}</h4>
                <Badge className={getNoticeType(notice.type)}>
                  {notice.type}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 mb-2">{notice.message}</p>
              <p className="text-xs text-gray-500">{notice.date}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default AnnouncementsNotices;
