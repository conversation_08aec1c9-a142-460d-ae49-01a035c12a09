import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import Logo from '@/components/Logo';
import ChatAssistant from '@/components/ChatAssistant';
import { ArrowLeft } from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  showBackButton?: boolean;
  className?: string;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  sidebar,
  header,
  title,
  subtitle,
  actions,
  showBackButton = true,
  className = ""
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 ${className}`}>
      {/* Custom Header or Default Header */}
      {header || (
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                {showBackButton && (
                  <Button variant="ghost" asChild className="text-gray-900 hover:text-gray-700">
                    <Link to="/">
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back to Home
                    </Link>
                  </Button>
                )}
                <div className="h-6 w-px bg-gray-300"></div>
                <Logo size="md" />
                {title && <span className="text-sm text-gray-500">{title}</span>}
              </div>
              {actions && (
                <div className="flex items-center space-x-4">
                  {actions}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex">
        {/* Sidebar */}
        {sidebar && (
          <div className="w-64 bg-white shadow-lg h-screen sticky top-0">
            {sidebar}
          </div>
        )}

        {/* Main Content */}
        <div className={`flex-1 ${sidebar ? 'p-8' : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'}`}>
          {/* Page Header */}
          {(title || subtitle) && !header && (
            <div className="flex items-center justify-between mb-8">
              <div>
                {title && <h1 className="text-3xl font-bold text-gray-900">{title}</h1>}
                {subtitle && <p className="text-gray-600">{subtitle}</p>}
              </div>
              {actions && (
                <div className="flex items-center space-x-4">
                  {actions}
                </div>
              )}
            </div>
          )}

          {/* Main Content */}
          {children}
        </div>
      </div>

      {/* Chat Assistant */}
      <ChatAssistant />
    </div>
  );
};

export default DashboardLayout;
