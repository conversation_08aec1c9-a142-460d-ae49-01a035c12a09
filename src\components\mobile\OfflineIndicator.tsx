import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { WifiOff, Wifi, Refresh<PERSON>w, AlertTriangle } from 'lucide-react';

interface OfflineData {
  salesReports: any[];
  inventoryUpdates: any[];
  lastSync: Date | null;
}

const OfflineIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineData, setOfflineData] = useState<OfflineData>({
    salesReports: [],
    inventoryUpdates: [],
    lastSync: null
  });
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      syncOfflineData();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Load offline data from localStorage
    loadOfflineData();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const loadOfflineData = () => {
    try {
      const stored = localStorage.getItem('franchisehub-offline-data');
      if (stored) {
        const data = JSON.parse(stored);
        setOfflineData({
          ...data,
          lastSync: data.lastSync ? new Date(data.lastSync) : null
        });
      }
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  };

  const saveOfflineData = (data: OfflineData) => {
    try {
      localStorage.setItem('franchisehub-offline-data', JSON.stringify(data));
      setOfflineData(data);
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  };

  const syncOfflineData = async () => {
    if (!isOnline || isSyncing) return;

    setIsSyncing(true);
    
    try {
      // Simulate API sync
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear offline data after successful sync
      const clearedData: OfflineData = {
        salesReports: [],
        inventoryUpdates: [],
        lastSync: new Date()
      };
      
      saveOfflineData(clearedData);
      
      console.log('Offline data synced successfully');
    } catch (error) {
      console.error('Error syncing offline data:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const addOfflineData = (type: 'salesReports' | 'inventoryUpdates', data: any) => {
    const newOfflineData = {
      ...offlineData,
      [type]: [...offlineData[type], { ...data, timestamp: new Date() }]
    };
    saveOfflineData(newOfflineData);
  };

  const getTotalPendingItems = () => {
    return offlineData.salesReports.length + offlineData.inventoryUpdates.length;
  };

  const formatLastSync = () => {
    if (!offlineData.lastSync) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - offlineData.lastSync.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  // Don't show if online and no pending data
  if (isOnline && getTotalPendingItems() === 0) {
    return null;
  }

  return (
    <Card className={`fixed top-4 right-4 z-40 w-80 shadow-lg ${
      isOnline ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="w-5 h-5 text-green-600" />
            ) : (
              <WifiOff className="w-5 h-5 text-orange-600" />
            )}
            <span className={`font-medium ${
              isOnline ? 'text-green-800' : 'text-orange-800'
            }`}>
              {isOnline ? 'Online' : 'Offline Mode'}
            </span>
            {getTotalPendingItems() > 0 && (
              <Badge variant="outline" className={
                isOnline ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
              }>
                {getTotalPendingItems()} pending
              </Badge>
            )}
          </div>
          
          {isOnline && getTotalPendingItems() > 0 && (
            <Button
              size="sm"
              onClick={syncOfflineData}
              disabled={isSyncing}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSyncing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>

        {!isOnline && (
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-orange-700">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">You're working offline</span>
            </div>
            <p className="text-xs text-orange-600">
              Your changes will be saved locally and synced when you're back online.
            </p>
          </div>
        )}

        {getTotalPendingItems() > 0 && (
          <div className="space-y-2 mt-3">
            <h4 className={`text-sm font-medium ${
              isOnline ? 'text-green-800' : 'text-orange-800'
            }`}>
              Pending Sync
            </h4>
            
            {offlineData.salesReports.length > 0 && (
              <div className="text-xs">
                <span className="font-medium">Sales Reports:</span> {offlineData.salesReports.length}
              </div>
            )}
            
            {offlineData.inventoryUpdates.length > 0 && (
              <div className="text-xs">
                <span className="font-medium">Inventory Updates:</span> {offlineData.inventoryUpdates.length}
              </div>
            )}
          </div>
        )}

        <div className={`text-xs mt-3 ${
          isOnline ? 'text-green-600' : 'text-orange-600'
        }`}>
          Last sync: {formatLastSync()}
        </div>

        {isSyncing && (
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
              <span className="text-sm text-blue-800">Syncing data...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Export the hook for other components to use
export const useOfflineData = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const saveOfflineData = (type: string, data: any) => {
    if (!isOnline) {
      try {
        const existing = localStorage.getItem('franchisehub-offline-data');
        const offlineData = existing ? JSON.parse(existing) : { salesReports: [], inventoryUpdates: [] };
        
        if (type === 'salesReport') {
          offlineData.salesReports.push({ ...data, timestamp: new Date() });
        } else if (type === 'inventoryUpdate') {
          offlineData.inventoryUpdates.push({ ...data, timestamp: new Date() });
        }
        
        localStorage.setItem('franchisehub-offline-data', JSON.stringify(offlineData));
        return true;
      } catch (error) {
        console.error('Error saving offline data:', error);
        return false;
      }
    }
    return false;
  };

  return { isOnline, saveOfflineData };
};

export default OfflineIndicator;
