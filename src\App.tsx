
import React, { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import EnhancedErrorBoundary from "@/components/error/EnhancedErrorBoundary";
import { PageLoading } from "@/components/ui/loading";
import { validateConfig } from "@/config/environment";
import { AuthProvider } from "@/components/auth/AuthProvider";

// Lazy load pages for better performance
const Index = React.lazy(() => import("./pages/Index"));
const Apply = React.lazy(() => import("./pages/Apply"));
const FranchisorDashboard = React.lazy(() => import("./pages/FranchisorDashboard"));
const FranchiseeDashboard = React.lazy(() => import("./pages/FranchiseeDashboard"));
const FranchiseeTraining = React.lazy(() => import("./pages/FranchiseeTraining"));
const BrandMicrosite = React.lazy(() => import("./pages/BrandMicrosite"));
const Blog = React.lazy(() => import("./pages/Blog"));
const BlogPost = React.lazy(() => import("./pages/BlogPost"));
const Contact = React.lazy(() => import("./pages/Contact"));
const SalesUpload = React.lazy(() => import("./pages/franchisee/SalesUpload"));
const InventoryOrder = React.lazy(() => import("./pages/franchisee/InventoryOrder"));
const MarketingAssets = React.lazy(() => import("./pages/franchisee/MarketingAssets"));
const ContractPackage = React.lazy(() => import("./pages/franchisee/ContractPackage"));
const SupportRequests = React.lazy(() => import("./pages/franchisee/SupportRequests"));
const NotFound = React.lazy(() => import("./pages/NotFound"));
const SupabaseTest = React.lazy(() => import("./components/SupabaseTest"));
const AdvancedAnalyticsPage = React.lazy(() => import("./pages/AdvancedAnalytics"));
const Login = React.lazy(() => import("./pages/Login"));
const ProtectedRoute = React.lazy(() => import("./components/auth/ProtectedRoute"));
const PWAInstallPrompt = React.lazy(() => import("./components/mobile/PWAInstallPrompt"));
const OfflineIndicator = React.lazy(() => import("./components/mobile/OfflineIndicator"));
const NotificationProvider = React.lazy(() => import("./components/notifications/NotificationProvider"));

// Configure React Query with better defaults
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Validate configuration on app start
validateConfig();

const App = () => (
  <EnhancedErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <Suspense fallback={null}>
            <NotificationProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
            <Suspense fallback={<PageLoading />}>
              <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/apply" element={<Apply />} />
              <Route path="/franchisor-dashboard" element={
                <ProtectedRoute requiredRole="franchisor">
                  <FranchisorDashboard />
                </ProtectedRoute>
              } />
              <Route path="/franchisee-dashboard" element={
                <ProtectedRoute requiredRole="franchisee">
                  <FranchiseeDashboard />
                </ProtectedRoute>
              } />
              <Route path="/franchisee-training" element={
                <ProtectedRoute>
                  <FranchiseeTraining />
                </ProtectedRoute>
              } />
              <Route path="/brand/:brandId" element={<BrandMicrosite />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:id" element={<BlogPost />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/franchisee/sales-upload" element={
                <ProtectedRoute requiredRole="franchisee">
                  <SalesUpload />
                </ProtectedRoute>
              } />
              <Route path="/franchisee/inventory-order" element={
                <ProtectedRoute requiredRole="franchisee">
                  <InventoryOrder />
                </ProtectedRoute>
              } />
              <Route path="/franchisee/marketing-assets" element={
                <ProtectedRoute requiredRole="franchisee">
                  <MarketingAssets />
                </ProtectedRoute>
              } />
              <Route path="/franchisee/contract-package" element={
                <ProtectedRoute requiredRole="franchisee">
                  <ContractPackage />
                </ProtectedRoute>
              } />
              <Route path="/franchisee/support-requests" element={
                <ProtectedRoute requiredRole="franchisee">
                  <SupportRequests />
                </ProtectedRoute>
              } />
              <Route path="/supabase-test" element={<SupabaseTest />} />
              <Route path="/advanced-analytics" element={<AdvancedAnalyticsPage />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
                <Suspense fallback={null}>
                  <PWAInstallPrompt />
                </Suspense>
                <Suspense fallback={null}>
                  <OfflineIndicator />
                </Suspense>
              </BrowserRouter>
            </NotificationProvider>
          </Suspense>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </EnhancedErrorBoundary>
);

export default App;
