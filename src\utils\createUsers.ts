import { supabase } from '@/lib/supabase';

export interface CreateUserData {
  email: string;
  password: string;
  fullName: string;
  role: 'admin' | 'franchisor' | 'franchisee';
}

export const createUser = async (userData: CreateUserData) => {
  if (!supabase) {
    throw new Error('Supabase not configured');
  }

  try {
    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          role: userData.role
        }
      }
    });

    if (authError) {
      throw authError;
    }

    if (!authData.user) {
      throw new Error('Failed to create user');
    }

    // Create profile
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: userData.email,
        full_name: userData.fullName,
        role: userData.role
      });

    if (profileError) {
      console.error('Profile creation error:', profileError);
      // Don't throw here as the user was created successfully
    }

    // If franchisee, create franchisee record
    if (userData.role === 'franchisee') {
      const { error: franchiseeError } = await supabase
        .from('franchisees')
        .insert({
          profile_id: authData.user.id,
          franchise_name: `${userData.fullName}'s Franchise`,
          brand: 'Siomai Shop', // Default brand
          location: 'Makati City', // Default location
          opening_date: new Date().toISOString().split('T')[0],
          monthly_sales_target: 50000,
          status: 'active'
        });

      if (franchiseeError) {
        console.error('Franchisee record creation error:', franchiseeError);
      }
    }

    return {
      success: true,
      user: authData.user,
      message: `User ${userData.email} created successfully`
    };

  } catch (error: any) {
    console.error('Error creating user:', error);
    return {
      success: false,
      error: error.message || 'Failed to create user'
    };
  }
};

export const createDefaultUsers = async () => {
  const users: CreateUserData[] = [
    {
      email: '<EMAIL>',
      password: 'Admin123',
      fullName: 'Admin',
      role: 'admin'
    },
    {
      email: '<EMAIL>',
      password: 'Owner123',
      fullName: 'Owner',
      role: 'franchisor'
    },
    {
      email: '<EMAIL>',
      password: 'Franchisee123',
      fullName: 'Franchisee1',
      role: 'franchisee'
    }
  ];

  const results = [];

  for (const userData of users) {
    console.log(`Creating user: ${userData.email}`);
    const result = await createUser(userData);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${result.message}`);
    } else {
      console.log(`❌ Failed to create ${userData.email}: ${result.error}`);
    }
  }

  return results;
};

// Function to check if users exist
export const checkUsersExist = async () => {
  if (!supabase) {
    throw new Error('Supabase not configured');
  }

  const emails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
  ];

  const { data: profiles, error } = await supabase
    .from('profiles')
    .select('email, full_name, role')
    .in('email', emails);

  if (error) {
    console.error('Error checking users:', error);
    return [];
  }

  return profiles || [];
};
