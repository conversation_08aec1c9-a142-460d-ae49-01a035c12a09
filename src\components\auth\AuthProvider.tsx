import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, metadata?: any) => Promise<any>;
  signOut: () => Promise<void>;
  profile: any;
  devMode: boolean;
  setDevMode: (enabled: boolean) => void;
  switchRole: (role: 'franchisor' | 'franchisee') => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<any>(null);
  const [devMode, setDevMode] = useState(false);
  const [devProfile, setDevProfile] = useState<any>(null);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
      }
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });

    // Create profile after successful signup
    if (data.user && !error) {
      try {
        await supabase.from('profiles').insert({
          id: data.user.id,
          email: data.user.email!,
          full_name: metadata?.full_name || '',
          role: metadata?.role || 'franchisee'
        });
      } catch (profileError) {
        console.error('Error creating profile:', profileError);
      }
    }

    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  // Dev mode functions
  const switchRole = (role: 'franchisor' | 'franchisee') => {
    if (devMode) {
      setDevProfile({
        id: 'dev-user',
        email: '<EMAIL>',
        full_name: 'Dev User',
        role: role,
        created_at: new Date().toISOString()
      });
    }
  };

  // Initialize dev mode on first load
  useEffect(() => {
    const isDev = import.meta.env.DEV || localStorage.getItem('devMode') === 'true';
    if (isDev) {
      setDevMode(true);
      switchRole('franchisor'); // Default to franchisor in dev mode
    }
  }, []);

  const value = {
    user: devMode ? ({ id: 'dev-user', email: '<EMAIL>' } as User) : user,
    session: devMode ? ({ user: { id: 'dev-user' } } as Session) : session,
    loading: devMode ? false : loading,
    signIn,
    signUp,
    signOut,
    profile: devMode ? devProfile : profile,
    devMode,
    setDevMode,
    switchRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hook for role-based access
export function useRole() {
  const { profile } = useAuth();

  return {
    role: profile?.role,
    isFranchisor: profile?.role === 'franchisor',
    isFranchisee: profile?.role === 'franchisee',
    isAdmin: profile?.role === 'admin'
  };
}
