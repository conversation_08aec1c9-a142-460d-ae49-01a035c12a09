import React, { createContext, useContext, useState, useEffect } from 'react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  timestamp: Date;
  read: boolean;
  urgent: boolean;
  category: 'sales' | 'inventory' | 'system' | 'training' | 'support';
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  urgentCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Low Stock Alert',
      message: 'Sauce Packets are running low (12 remaining)',
      type: 'warning',
      timestamp: new Date(),
      read: false,
      urgent: true,
      category: 'inventory'
    },
    {
      id: '2',
      title: 'Sales Target Achieved',
      message: 'Congratulations! You\'ve reached 85% of monthly target',
      type: 'success',
      timestamp: new Date(Date.now() - 3600000),
      read: false,
      urgent: false,
      category: 'sales'
    },
    {
      id: '3',
      title: 'Training Due',
      message: 'Monthly compliance training due by January 20th',
      type: 'info',
      timestamp: new Date(Date.now() - 7200000),
      read: true,
      urgent: false,
      category: 'training'
    }
  ]);

  const unreadCount = notifications.filter(n => !n.read).length;
  const urgentCount = notifications.filter(n => !n.read && n.urgent).length;

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add notifications for demo purposes
      if (Math.random() > 0.95) {
        const demoNotifications = [
          {
            title: 'New Order Received',
            message: 'A new inventory order has been processed',
            type: 'info' as const,
            urgent: false,
            category: 'inventory' as const
          },
          {
            title: 'Daily Sales Update',
            message: 'Your daily sales report is ready for review',
            type: 'success' as const,
            urgent: false,
            category: 'sales' as const
          }
        ];
        
        const randomNotification = demoNotifications[Math.floor(Math.random() * demoNotifications.length)];
        addNotification(randomNotification);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const value = {
    notifications,
    unreadCount,
    urgentCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
