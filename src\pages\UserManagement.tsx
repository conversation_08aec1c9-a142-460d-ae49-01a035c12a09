import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createUser, createDefaultUsers, checkUsersExist, CreateUserData } from '@/utils/createUsers';
import { Users, Plus, CheckCircle, XCircle } from 'lucide-react';

const UserManagement = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [existingUsers, setExistingUsers] = useState<any[]>([]);
  const [formData, setFormData] = useState<CreateUserData>({
    email: '',
    password: '',
    fullName: '',
    role: 'franchisee'
  });

  useEffect(() => {
    loadExistingUsers();
  }, []);

  const loadExistingUsers = async () => {
    try {
      const users = await checkUsersExist();
      setExistingUsers(users);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const handleCreateDefaultUsers = async () => {
    setLoading(true);
    setMessage(null);

    try {
      const results = await createDefaultUsers();
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        setMessage({
          type: 'success',
          text: `Created ${successCount} users successfully${failCount > 0 ? `, ${failCount} failed` : ''}`
        });
        await loadExistingUsers();
      } else {
        setMessage({
          type: 'error',
          text: 'Failed to create any users'
        });
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || 'Failed to create users'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    try {
      const result = await createUser(formData);
      
      if (result.success) {
        setMessage({
          type: 'success',
          text: result.message || 'User created successfully'
        });
        setFormData({
          email: '',
          password: '',
          fullName: '',
          role: 'franchisee'
        });
        await loadExistingUsers();
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'Failed to create user'
        });
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || 'Failed to create user'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600 mt-2">Create and manage system users</p>
        </div>

        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {message.type === 'success' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid md:grid-cols-2 gap-6">
          {/* Quick Setup */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>Quick Setup</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                Create the default system users with predefined credentials:
              </p>
              
              <div className="space-y-2 text-sm">
                <div className="p-3 bg-gray-50 rounded">
                  <strong>Admin:</strong> <EMAIL> / Admin123
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <strong>Owner:</strong> <EMAIL> / Owner123
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <strong>Franchisee1:</strong> <EMAIL> / Franchisee123
                </div>
              </div>

              <Button 
                onClick={handleCreateDefaultUsers} 
                disabled={loading}
                className="w-full"
              >
                {loading ? 'Creating Users...' : 'Create Default Users'}
              </Button>
            </CardContent>
          </Card>

          {/* Custom User Creation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="w-5 h-5" />
                <span>Create Custom User</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateCustomUser} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    required
                    minLength={6}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={formData.role} onValueChange={(value: any) => setFormData({ ...formData, role: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="franchisor">Franchisor</SelectItem>
                      <SelectItem value="franchisee">Franchisee</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button type="submit" disabled={loading} className="w-full">
                  {loading ? 'Creating...' : 'Create User'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Existing Users */}
        {existingUsers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Existing Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {existingUsers.map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <span className="font-medium">{user.full_name}</span>
                      <span className="text-gray-600 ml-2">({user.email})</span>
                    </div>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                      {user.role}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default UserManagement;
