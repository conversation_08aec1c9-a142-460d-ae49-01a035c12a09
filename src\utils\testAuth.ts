// Authentication testing utilities
import { supabase } from '@/lib/supabase';

export interface TestUser {
  email: string;
  password: string;
  fullName: string;
  role: 'admin' | 'franchisor' | 'franchisee';
}

export const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'Admin123',
    fullName: 'Admin',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'Owner123',
    fullName: 'Owner',
    role: 'franchisor'
  },
  {
    email: '<EMAIL>',
    password: 'Franchisee123',
    fullName: 'Franchisee1',
    role: 'franchisee'
  }
];

export const testLogin = async (email: string, password: string) => {
  if (!supabase) {
    console.error('❌ Supabase not configured');
    return { success: false, error: 'Supabase not configured' };
  }

  try {
    console.log(`🔐 Testing login for: ${email}`);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error(`❌ Login failed for ${email}:`, error.message);
      return { success: false, error: error.message };
    }

    if (data.user) {
      console.log(`✅ Login successful for ${email}`);
      
      // Fetch profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) {
        console.warn(`⚠️ Profile fetch failed for ${email}:`, profileError.message);
      } else {
        console.log(`👤 Profile loaded for ${email}:`, profile);
      }

      return { 
        success: true, 
        user: data.user, 
        profile,
        session: data.session 
      };
    }

    return { success: false, error: 'No user data received' };
  } catch (err: any) {
    console.error(`💥 Login exception for ${email}:`, err);
    return { success: false, error: err.message };
  }
};

export const testAllUsers = async () => {
  console.log('🚀 Starting authentication tests...');
  
  const results = [];
  
  for (const user of testUsers) {
    const result = await testLogin(user.email, user.password);
    results.push({
      user: user.email,
      role: user.role,
      ...result
    });
    
    // Sign out after each test
    if (result.success && supabase) {
      await supabase.auth.signOut();
      console.log(`🚪 Signed out ${user.email}`);
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('📊 Test Results Summary:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.user} (${result.role}): ${result.success ? 'SUCCESS' : result.error}`);
  });
  
  return results;
};

export const checkSupabaseConnection = async () => {
  if (!supabase) {
    console.error('❌ Supabase client not initialized');
    return false;
  }

  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error('❌ Supabase connection error:', error);
      return false;
    }
    
    console.log('✅ Supabase connection successful');
    return true;
  } catch (err) {
    console.error('❌ Supabase connection exception:', err);
    return false;
  }
};

export const checkUserExists = async (email: string) => {
  if (!supabase) return false;

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('email, full_name, role')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error(`Error checking user ${email}:`, error);
      return false;
    }

    return !!data;
  } catch (err) {
    console.error(`Exception checking user ${email}:`, err);
    return false;
  }
};

export const checkAllUsersExist = async () => {
  console.log('👥 Checking if test users exist...');
  
  for (const user of testUsers) {
    const exists = await checkUserExists(user.email);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${user.email} (${user.role}): ${exists ? 'EXISTS' : 'NOT FOUND'}`);
  }
};

// Browser console helpers
if (typeof window !== 'undefined') {
  (window as any).testAuth = {
    testLogin,
    testAllUsers,
    checkSupabaseConnection,
    checkUserExists,
    checkAllUsersExist,
    testUsers
  };
  
  console.log('🔧 Auth testing utilities loaded. Available commands:');
  console.log('- testAuth.checkSupabaseConnection()');
  console.log('- testAuth.checkAllUsersExist()');
  console.log('- testAuth.testLogin(email, password)');
  console.log('- testAuth.testAllUsers()');
}
