import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import Logo from '@/components/Logo';
import { ArrowLeft, LucideIcon } from 'lucide-react';

interface SidebarItem {
  label: string;
  href: string;
  icon: LucideIcon;
  active?: boolean;
  variant?: 'default' | 'primary';
}

interface DashboardSidebarProps {
  items: SidebarItem[];
  primaryAction?: {
    label: string;
    href: string;
    icon: LucideIcon;
  };
  showBackButton?: boolean;
}

const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  items,
  primaryAction,
  showBackButton = true
}) => {
  const location = useLocation();

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-8">
        <Logo size="md" />
        {showBackButton && (
          <Button variant="ghost" asChild size="sm" className="text-gray-900 hover:text-gray-700 p-1">
            <Link to="/">
              <ArrowLeft className="w-4 h-4" />
            </Link>
          </Button>
        )}
      </div>

      <nav className="space-y-2">
        {items.map((item, index) => {
          const active = item.active !== undefined ? item.active : isActive(item.href);
          const Icon = item.icon;

          if (item.variant === 'primary') {
            return (
              <Button 
                key={index}
                asChild 
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 mt-4"
              >
                <Link to={item.href}>
                  <Icon className="w-4 h-4 mr-2" />
                  {item.label}
                </Link>
              </Button>
            );
          }

          return (
            <Link
              key={index}
              to={item.href}
              className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                active
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{item.label}</span>
            </Link>
          );
        })}

        {primaryAction && (
          <Button asChild className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 mt-4">
            <Link to={primaryAction.href}>
              <primaryAction.icon className="w-4 h-4 mr-2" />
              {primaryAction.label}
            </Link>
          </Button>
        )}
      </nav>
    </div>
  );
};

export default DashboardSidebar;
